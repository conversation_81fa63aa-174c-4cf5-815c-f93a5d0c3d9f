package com.qudian.idle.customer.center.server.facade.impl.app;

import com.qudian.idle.customer.center.api.facade.app.CustomerBaseAppFacade;
import com.qudian.idle.customer.center.api.vo.request.base.CustomerEditReqVO;
import com.qudian.idle.customer.center.api.vo.request.base.CustomerShowReqVO;
import com.qudian.idle.customer.center.api.vo.response.base.CustomerShowRespVO;
import com.qudian.idle.customer.center.business.service.base.CustomerBaseService;
import com.qudian.lme.base.builder.ResponseBuilder;
import com.qudian.lme.common.dto.BaseResponseDTO;
import com.qudian.pdt.api.toolkit.vo.response.BaseResponseVO;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * <p>文件名称:com.qudian.idle.customer.center.server.facade.impl.app.CustomerBaseAppFacadeImpl</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
@DubboService
public class CustomerBaseAppFacadeImpl implements CustomerBaseAppFacade {

    @Resource
    private CustomerBaseService customerBaseService;

    @Override
    public BaseResponseDTO<BaseResponseVO> addOrEdit(CustomerEditReqVO reqVO) {
        customerBaseService.createOrUpdate(reqVO);
        return ResponseBuilder.buildSuccess(new BaseResponseVO());
    }

    @Override
    public BaseResponseDTO<CustomerShowRespVO> show(CustomerShowReqVO reqVO) {
        return ResponseBuilder.buildSuccess(customerBaseService.query(reqVO));
    }
}
