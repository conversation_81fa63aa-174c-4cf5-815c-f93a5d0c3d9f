package com.qudian.idle.customer.center.server;

import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2024</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> ` s architecture board)
 * @version 1.0
 */
@Rollback
@Transactional
@ActiveProfiles("local")
@SpringBootTest(classes = {StartServerApplication.class}, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//@TypeExcludeFilters({TestExcludeFilter.class})
public class StartTestApplication {
}
