package com.qudian.idle.customer.center.api.vo;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>文件名称:com.qudian.Codele.customer.center.api.vo.OptionRecord</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
@Data
@AllArgsConstructor
public class OptionRecord implements Serializable {
    private static final long serialVersionUID = -101678720028544738L;
    private Integer code;
    private String title;
}
