package com.qudian.idle.customer.center.api.vo.request.base;

import com.qudian.idle.customer.center.api.vo.OptionWrapper;
import com.qudian.pdt.api.toolkit.vo.request.BaseRequestVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>文件名称:com.qudian.idle.customer.center.api.vo.request.base.CustomerEditReqVOExample</p>
 * <p>文件描述: 使用OptionWrapper的客户编辑请求VO示例</p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: 展示如何使用OptionWrapper来处理前端传入的选项数据</p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerEditReqVOExample extends BaseRequestVO {
    private static final long serialVersionUID = -40915276506458728L;

    private CustomerEditBaseReqVOExample base;
    private CustomerEditExtReqVOExample preference;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CustomerEditBaseReqVOExample implements Serializable {
        private static final long serialVersionUID = 6741371488551945754L;

        private Long id;
        private String name;
        private String mobile;
        private String wechatId; //客户微信号
        
        // 使用OptionWrapper替代List<OptionRecord>
        private OptionWrapper gender; //性别 1(男), 2(女) - 单选
        private OptionWrapper ageGroup; //客户年龄段: 1(<24), 2(25-34), 3(35-44), 4(45-54), 5(55+) - 单选
        private OptionWrapper source; //客户来源: 1(线上引流), 2(线下渠道) - 单选
        private OptionWrapper homeVisitStatus; //是否接受上门: 1(接受上门), 2(不接受上门) - 单选
        private OptionWrapper outputIntention; //客户出售意向: 1(高意向), 2(中意向), 3(低意向), 4(无意向), 5(不确定) - 单选
        private OptionWrapper membershipType; //会员类型: 1(普通), 2（VIP会员） - 单选
        private OptionWrapper purchaseIntention; //客户购买意向: 1(高意向), 2(中意向), 3(低意向), 4(无意向), 5(不确定) - 单选
        
        private String province; //地址-省份
        private String city; //地址-城市
        private String district; //地址-区域
        private String detailAddress; //地址-详细地址
        private String location; //地址-经纬度
        private String remark; //备注
        private String images; //备注图
        
        // 便捷方法：获取单值字段的code
        public Integer getGenderCode() {
            return gender != null ? gender.getSingleCode() : null;
        }
        
        public Integer getAgeGroupCode() {
            return ageGroup != null ? ageGroup.getSingleCode() : null;
        }
        
        public Integer getSourceCode() {
            return source != null ? source.getSingleCode() : null;
        }
        
        public Integer getHomeVisitStatusCode() {
            return homeVisitStatus != null ? homeVisitStatus.getSingleCode() : null;
        }
        
        public Integer getOutputIntentionCode() {
            return outputIntention != null ? outputIntention.getSingleCode() : null;
        }
        
        public Integer getMembershipTypeCode() {
            return membershipType != null ? membershipType.getSingleCode() : null;
        }
        
        public Integer getPurchaseIntentionCode() {
            return purchaseIntention != null ? purchaseIntention.getSingleCode() : null;
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CustomerEditExtReqVOExample implements Serializable {
        private static final long serialVersionUID = 3963234033724077384L;

        // 这些字段可能是多选的，所以使用OptionWrapper
        private OptionWrapper preferType; //偏好类型 - 可能多选
        private OptionWrapper bag; //包袋品牌偏好 - 可能多选
        private OptionWrapper jewel;  //珠宝品牌偏好 - 可能多选
        private OptionWrapper watches;  //手表品牌偏好 - 可能多选
        
        // 便捷方法：根据业务需求获取单值或多值
        public java.util.List<Integer> getPreferTypeCodes() {
            return preferType != null ? preferType.getMultipleCodes() : java.util.Collections.emptyList();
        }
        
        public java.util.List<Integer> getBagCodes() {
            return bag != null ? bag.getMultipleCodes() : java.util.Collections.emptyList();
        }
        
        public java.util.List<Integer> getJewelCodes() {
            return jewel != null ? jewel.getMultipleCodes() : java.util.Collections.emptyList();
        }
        
        public java.util.List<Integer> getWatchesCodes() {
            return watches != null ? watches.getMultipleCodes() : java.util.Collections.emptyList();
        }
    }
}
