package com.qudian.idle.customer.center.api.vo;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>文件名称:com.qudian.idle.customer.center.api.vo.OptionWrapper</p>
 * <p>文件描述: 选项包装器，用于处理前端传入的选项数据自动转换</p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: 
 *   解决前端传入 {"gender":[{"code":1, "title":"male"}]} 格式数据的自动转换问题
 *   支持单值和多值的自动提取和转换
 * </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
@Data
public class OptionWrapper implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private List<OptionRecord> options;
    
    public OptionWrapper() {
        this.options = new ArrayList<>();
    }
    
    public OptionWrapper(List<OptionRecord> options) {
        this.options = options != null ? options : new ArrayList<>();
    }
    
    /**
     * Jackson反序列化时直接接收List<OptionRecord>
     */
    @JsonCreator
    public static OptionWrapper fromList(List<OptionRecord> options) {
        return new OptionWrapper(options);
    }
    
    /**
     * Jackson序列化时直接输出List<OptionRecord>
     */
    @JsonValue
    public List<OptionRecord> toList() {
        return this.options;
    }
    
    /**
     * 获取第一个选项的code值，用于单值字段
     * @return 第一个选项的code，如果没有选项则返回null
     */
    public Integer getSingleCode() {
        if (options == null || options.isEmpty()) {
            return null;
        }
        return options.get(0).getCode();
    }
    
    /**
     * 获取所有选项的code值列表，用于多值字段
     * @return 所有选项的code列表
     */
    public List<Integer> getMultipleCodes() {
        if (options == null || options.isEmpty()) {
            return Collections.emptyList();
        }
        return options.stream()
                .map(OptionRecord::getCode)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取第一个选项的title值
     * @return 第一个选项的title，如果没有选项则返回null
     */
    public String getSingleTitle() {
        if (options == null || options.isEmpty()) {
            return null;
        }
        return options.get(0).getTitle();
    }
    
    /**
     * 获取所有选项的title值列表
     * @return 所有选项的title列表
     */
    public List<String> getMultipleTitles() {
        if (options == null || options.isEmpty()) {
            return Collections.emptyList();
        }
        return options.stream()
                .map(OptionRecord::getTitle)
                .collect(Collectors.toList());
    }
    
    /**
     * 判断是否为空
     * @return 是否为空
     */
    public boolean isEmpty() {
        return options == null || options.isEmpty();
    }
    
    /**
     * 获取选项数量
     * @return 选项数量
     */
    public int size() {
        return options == null ? 0 : options.size();
    }
    
    /**
     * 从单个code值创建OptionWrapper（用于数据库到VO的转换）
     * @param code code值
     * @param title title值
     * @return OptionWrapper实例
     */
    public static OptionWrapper fromSingle(Integer code, String title) {
        if (code == null) {
            return new OptionWrapper();
        }
        List<OptionRecord> options = Collections.singletonList(new OptionRecord(code, title));
        return new OptionWrapper(options);
    }
    
    /**
     * 从多个code值创建OptionWrapper（用于数据库到VO的转换）
     * @param codes code值列表
     * @param titleProvider 根据code获取title的函数接口
     * @return OptionWrapper实例
     */
    public static OptionWrapper fromMultiple(List<Integer> codes, java.util.function.Function<Integer, String> titleProvider) {
        if (codes == null || codes.isEmpty()) {
            return new OptionWrapper();
        }
        List<OptionRecord> options = codes.stream()
                .map(code -> new OptionRecord(code, titleProvider.apply(code)))
                .collect(Collectors.toList());
        return new OptionWrapper(options);
    }
    
    /**
     * 从JSON字符串创建OptionWrapper（用于数据库JSON字段的转换）
     * @param jsonString JSON字符串
     * @return OptionWrapper实例
     */
    public static OptionWrapper fromJson(String jsonString) {
        // 这里可以根据需要实现JSON解析逻辑
        // 暂时返回空的OptionWrapper
        return new OptionWrapper();
    }
    
    /**
     * 转换为JSON字符串（用于存储到数据库JSON字段）
     * @return JSON字符串
     */
    public String toJson() {
        // 这里可以根据需要实现JSON序列化逻辑
        // 暂时返回空字符串
        return "";
    }
}
