package com.qudian.idle.customer.center.api.vo;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <p>文件名称:com.qudian.idle.customer.center.api.vo.OptionWrapperTest</p>
 * <p>文件描述: OptionWrapper测试类</p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: 测试OptionWrapper的各种功能</p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
public class OptionWrapperTest {
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Test
    public void testJsonDeserialization() throws Exception {
        // 测试前端传入的JSON格式：{"gender":[{"code":1, "title":"male"}]}
        String json = """
            {
                "gender": [{"code": 1, "title": "男"}]
            }
            """;
        
        // 使用内部类来模拟接收JSON的结构
        class TestVO {
            public OptionWrapper gender;
        }
        
        TestVO testVO = objectMapper.readValue(json, TestVO.class);
        
        assertNotNull(testVO.gender);
        assertEquals(1, testVO.gender.size());
        assertEquals(Integer.valueOf(1), testVO.gender.getSingleCode());
        assertEquals("男", testVO.gender.getSingleTitle());
    }
    
    @Test
    public void testJsonSerialization() throws Exception {
        // 测试序列化为前端期望的格式
        OptionRecord option = new OptionRecord(1, "男");
        OptionWrapper wrapper = new OptionWrapper(Arrays.asList(option));
        
        class TestVO {
            public OptionWrapper gender = wrapper;
        }
        
        TestVO testVO = new TestVO();
        String json = objectMapper.writeValueAsString(testVO);
        
        // 验证序列化结果
        assertTrue(json.contains("\"gender\":[{\"code\":1,\"title\":\"男\"}]"));
    }
    
    @Test
    public void testSingleValueExtraction() {
        // 测试单值提取
        OptionRecord option = new OptionRecord(1, "男");
        OptionWrapper wrapper = new OptionWrapper(Arrays.asList(option));
        
        assertEquals(Integer.valueOf(1), wrapper.getSingleCode());
        assertEquals("男", wrapper.getSingleTitle());
    }
    
    @Test
    public void testMultipleValueExtraction() {
        // 测试多值提取
        List<OptionRecord> options = Arrays.asList(
            new OptionRecord(1, "包袋"),
            new OptionRecord(2, "珠宝"),
            new OptionRecord(3, "手表")
        );
        OptionWrapper wrapper = new OptionWrapper(options);
        
        List<Integer> codes = wrapper.getMultipleCodes();
        List<String> titles = wrapper.getMultipleTitles();
        
        assertEquals(3, codes.size());
        assertEquals(Arrays.asList(1, 2, 3), codes);
        assertEquals(Arrays.asList("包袋", "珠宝", "手表"), titles);
    }
    
    @Test
    public void testEmptyWrapper() {
        // 测试空包装器
        OptionWrapper wrapper = new OptionWrapper();
        
        assertTrue(wrapper.isEmpty());
        assertEquals(0, wrapper.size());
        assertNull(wrapper.getSingleCode());
        assertNull(wrapper.getSingleTitle());
        assertTrue(wrapper.getMultipleCodes().isEmpty());
        assertTrue(wrapper.getMultipleTitles().isEmpty());
    }
    
    @Test
    public void testFromSingleFactory() {
        // 测试从单个值创建
        OptionWrapper wrapper = OptionWrapper.fromSingle(1, "男");
        
        assertFalse(wrapper.isEmpty());
        assertEquals(1, wrapper.size());
        assertEquals(Integer.valueOf(1), wrapper.getSingleCode());
        assertEquals("男", wrapper.getSingleTitle());
    }
    
    @Test
    public void testFromMultipleFactory() {
        // 测试从多个值创建
        List<Integer> codes = Arrays.asList(1, 2, 3);
        OptionWrapper wrapper = OptionWrapper.fromMultiple(codes, code -> "选项" + code);
        
        assertFalse(wrapper.isEmpty());
        assertEquals(3, wrapper.size());
        assertEquals(codes, wrapper.getMultipleCodes());
        assertEquals(Arrays.asList("选项1", "选项2", "选项3"), wrapper.getMultipleTitles());
    }
    
    @Test
    public void testNullSafety() {
        // 测试空值安全性
        OptionWrapper wrapper = new OptionWrapper(null);
        
        assertTrue(wrapper.isEmpty());
        assertEquals(0, wrapper.size());
        assertNull(wrapper.getSingleCode());
        assertTrue(wrapper.getMultipleCodes().isEmpty());
        
        // 测试从null创建
        OptionWrapper nullWrapper = OptionWrapper.fromSingle(null, null);
        assertTrue(nullWrapper.isEmpty());
        
        OptionWrapper nullMultipleWrapper = OptionWrapper.fromMultiple(null, code -> "test");
        assertTrue(nullMultipleWrapper.isEmpty());
    }
}
