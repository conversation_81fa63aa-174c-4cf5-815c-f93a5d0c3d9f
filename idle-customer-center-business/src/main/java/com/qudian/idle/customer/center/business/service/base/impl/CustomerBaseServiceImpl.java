package com.qudian.idle.customer.center.business.service.base.impl;

import com.qudian.idle.customer.center.api.vo.request.base.CustomerEditReqVO;
import com.qudian.idle.customer.center.api.vo.request.base.CustomerShowReqVO;
import com.qudian.idle.customer.center.api.vo.response.base.CustomerShowRespVO;
import com.qudian.idle.customer.center.business.service.base.CustomerBaseService;
import com.qudian.idle.customer.center.infrastructure.assembler.CustomerBaseStruct;
import com.qudian.idle.customer.center.infrastructure.repository.database.CustomerBaseRepository;
import com.qudian.idle.customer.center.infrastructure.repository.database.CustomerExtRepository;
import com.qudian.idle.customer.center.infrastructure.repository.database.po.base.CustomerBasePO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <p>文件名称:com.qudian.idle.customer.center.business.service.base.impl.CustomerBaseServiceImpl</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
@Slf4j
@Service
public class CustomerBaseServiceImpl implements CustomerBaseService {
    @Resource
    private CustomerBaseRepository customerBaseRepository;
    @Resource
    private CustomerExtRepository customerExtRepository;
    @Resource
    private CustomerBaseStruct customerBaseStruct;

    @Override
    public void createOrUpdate(CustomerEditReqVO editReqVO) {
        CustomerEditReqVO.CustomerEditBaseReqVO base = editReqVO.getBase();
        if (Objects.isNull(base)) {

            return;
        }
        if (null != base.getId()) {    //for updating customer
            this.update();
            return;
        }
        this.create(editReqVO);  //create new customer
    }

    private void create(CustomerEditReqVO createVO) {
        CustomerEditReqVO.CustomerEditBaseReqVO createBase = createVO.getBase();
        CustomerBasePO customerBasePO = customerBaseStruct.baseVO2PO(createBase);
        customerBaseRepository.save(customerBasePO);
    }


    @Override
    public CustomerShowRespVO query(CustomerShowReqVO showReqVO) {
        return null;
    }
}
