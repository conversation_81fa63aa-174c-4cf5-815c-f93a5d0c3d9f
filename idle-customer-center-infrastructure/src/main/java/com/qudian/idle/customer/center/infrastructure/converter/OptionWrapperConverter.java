package com.qudian.idle.customer.center.infrastructure.converter;

import com.qudian.idle.customer.center.api.vo.OptionRecord;
import com.qudian.idle.customer.center.api.vo.OptionWrapper;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * <p>文件名称:com.qudian.idle.customer.center.infrastructure.converter.OptionWrapperConverter</p>
 * <p>文件描述: OptionWrapper转换器，处理各种数据格式之间的转换</p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: 提供OptionWrapper与数据库字段、JSON等格式之间的转换方法</p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
@Component
public class OptionWrapperConverter {
    
    // 预定义的选项映射，可以根据实际业务需求配置
    private static final Map<String, Map<Integer, String>> OPTION_MAPPINGS = Map.of(
        "gender", Map.of(1, "男", 2, "女"),
        "ageGroup", Map.of(1, "<24", 2, "25-34", 3, "35-44", 4, "45-54", 5, "55+"),
        "source", Map.of(1, "线上引流", 2, "线下渠道"),
        "homeVisitStatus", Map.of(1, "接受上门", 2, "不接受上门"),
        "outputIntention", Map.of(1, "高意向", 2, "中意向", 3, "低意向", 4, "无意向", 5, "不确定"),
        "membershipType", Map.of(1, "普通", 2, "VIP会员"),
        "purchaseIntention", Map.of(1, "高意向", 2, "中意向", 3, "低意向", 4, "无意向", 5, "不确定")
    );
    
    /**
     * 将数据库单值字段转换为OptionWrapper
     * @param fieldName 字段名称（用于获取title映射）
     * @param code 数据库中的code值
     * @return OptionWrapper实例
     */
    public OptionWrapper fromDatabaseSingleValue(String fieldName, Integer code) {
        if (code == null) {
            return new OptionWrapper();
        }
        
        String title = getTitleByCode(fieldName, code);
        return OptionWrapper.fromSingle(code, title);
    }
    
    /**
     * 将数据库多值字段转换为OptionWrapper
     * @param fieldName 字段名称（用于获取title映射）
     * @param codes 数据库中的code值列表
     * @return OptionWrapper实例
     */
    public OptionWrapper fromDatabaseMultipleValues(String fieldName, List<Integer> codes) {
        if (codes == null || codes.isEmpty()) {
            return new OptionWrapper();
        }
        
        Function<Integer, String> titleProvider = code -> getTitleByCode(fieldName, code);
        return OptionWrapper.fromMultiple(codes, titleProvider);
    }
    
    /**
     * 将OptionWrapper转换为数据库单值字段
     * @param optionWrapper OptionWrapper实例
     * @return 数据库中的code值
     */
    public Integer toDatabaseSingleValue(OptionWrapper optionWrapper) {
        return optionWrapper != null ? optionWrapper.getSingleCode() : null;
    }
    
    /**
     * 将OptionWrapper转换为数据库多值字段
     * @param optionWrapper OptionWrapper实例
     * @return 数据库中的code值列表
     */
    public List<Integer> toDatabaseMultipleValues(OptionWrapper optionWrapper) {
        return optionWrapper != null ? optionWrapper.getMultipleCodes() : Collections.emptyList();
    }
    
    /**
     * 将List<OptionRecord>转换为OptionWrapper
     * @param optionRecords OptionRecord列表
     * @return OptionWrapper实例
     */
    public OptionWrapper fromOptionRecordList(List<OptionRecord> optionRecords) {
        return new OptionWrapper(optionRecords);
    }
    
    /**
     * 将OptionWrapper转换为List<OptionRecord>
     * @param optionWrapper OptionWrapper实例
     * @return OptionRecord列表
     */
    public List<OptionRecord> toOptionRecordList(OptionWrapper optionWrapper) {
        return optionWrapper != null ? optionWrapper.toList() : Collections.emptyList();
    }
    
    /**
     * 根据字段名和code获取对应的title
     * @param fieldName 字段名称
     * @param code code值
     * @return title值
     */
    private String getTitleByCode(String fieldName, Integer code) {
        Map<Integer, String> mapping = OPTION_MAPPINGS.get(fieldName);
        if (mapping != null && mapping.containsKey(code)) {
            return mapping.get(code);
        }
        return "未知选项"; // 默认值
    }
    
    /**
     * 验证OptionWrapper是否有效（非空且包含有效选项）
     * @param optionWrapper OptionWrapper实例
     * @return 是否有效
     */
    public boolean isValid(OptionWrapper optionWrapper) {
        return optionWrapper != null && !optionWrapper.isEmpty();
    }
    
    /**
     * 验证OptionWrapper是否为单选（只包含一个选项）
     * @param optionWrapper OptionWrapper实例
     * @return 是否为单选
     */
    public boolean isSingleSelection(OptionWrapper optionWrapper) {
        return optionWrapper != null && optionWrapper.size() == 1;
    }
    
    /**
     * 验证OptionWrapper是否为多选（包含多个选项）
     * @param optionWrapper OptionWrapper实例
     * @return 是否为多选
     */
    public boolean isMultipleSelection(OptionWrapper optionWrapper) {
        return optionWrapper != null && optionWrapper.size() > 1;
    }
    
    /**
     * 创建空的OptionWrapper
     * @return 空的OptionWrapper实例
     */
    public OptionWrapper createEmpty() {
        return new OptionWrapper();
    }
    
    /**
     * 从单个code和title创建OptionWrapper
     * @param code code值
     * @param title title值
     * @return OptionWrapper实例
     */
    public OptionWrapper createSingle(Integer code, String title) {
        return OptionWrapper.fromSingle(code, title);
    }
}
