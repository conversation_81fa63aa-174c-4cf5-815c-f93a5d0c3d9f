package com.qudian.idle.customer.center.infrastructure.assembler;

import com.qudian.idle.customer.center.api.vo.OptionWrapper;
import com.qudian.idle.customer.center.api.vo.request.base.CustomerEditReqVOExample;
import com.qudian.idle.customer.center.api.vo.response.base.CustomerShowRespVO;
import com.qudian.idle.customer.center.infrastructure.converter.OptionWrapperConverter;
import com.qudian.idle.customer.center.infrastructure.repository.database.po.base.CustomerBasePO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <p>文件名称:com.qudian.idle.customer.center.infrastructure.assembler.CustomerBaseStructExample</p>
 * <p>文件描述: 使用OptionWrapper的客户基础信息转换器示例</p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: 展示如何在MapStruct中使用OptionWrapper进行数据转换</p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
@Mapper(componentModel = "spring")
public abstract class CustomerBaseStructExample {
    
    @Autowired
    protected OptionWrapperConverter optionWrapperConverter;
    
    /**
     * 将请求VO转换为数据库PO
     */
    @Mapping(target = "gender", source = "gender", qualifiedByName = "optionWrapperToInteger")
    @Mapping(target = "ageGroup", source = "ageGroup", qualifiedByName = "optionWrapperToInteger")
    @Mapping(target = "source", source = "source", qualifiedByName = "optionWrapperToInteger")
    @Mapping(target = "homeVisitStatus", source = "homeVisitStatus", qualifiedByName = "optionWrapperToInteger")
    @Mapping(target = "outputIntention", source = "outputIntention", qualifiedByName = "optionWrapperToInteger")
    @Mapping(target = "membershipType", source = "membershipType", qualifiedByName = "optionWrapperToInteger")
    @Mapping(target = "purchaseIntention", source = "purchaseIntention", qualifiedByName = "optionWrapperToInteger")
    public abstract CustomerBasePO baseVO2PO(CustomerEditReqVOExample.CustomerEditBaseReqVOExample reqVO);
    
    /**
     * 将数据库PO转换为响应VO
     */
    @Mapping(target = "gender", source = "gender", qualifiedByName = "integerToOptionWrapper")
    @Mapping(target = "ageGroup", source = "ageGroup", qualifiedByName = "integerToOptionWrapper")
    @Mapping(target = "source", source = "source", qualifiedByName = "integerToOptionWrapper")
    @Mapping(target = "homeVisitStatus", source = "homeVisitStatus", qualifiedByName = "integerToOptionWrapper")
    @Mapping(target = "outputIntention", source = "outputIntention", qualifiedByName = "integerToOptionWrapper")
    @Mapping(target = "membershipType", source = "membershipType", qualifiedByName = "integerToOptionWrapper")
    @Mapping(target = "purchaseIntention", source = "purchaseIntention", qualifiedByName = "integerToOptionWrapper")
    public abstract CustomerShowRespVO.CustomerShowBaseRespVO po2RespVO(CustomerBasePO po);
    
    /**
     * 将OptionWrapper转换为Integer（用于存储到数据库）
     */
    @Named("optionWrapperToInteger")
    protected Integer optionWrapperToInteger(OptionWrapper optionWrapper) {
        return optionWrapperConverter.toDatabaseSingleValue(optionWrapper);
    }
    
    /**
     * 将Integer转换为OptionWrapper（用于从数据库读取）
     * 注意：这里需要根据具体字段来确定fieldName，实际使用时可能需要为每个字段创建专门的方法
     */
    @Named("integerToOptionWrapper")
    protected OptionWrapper integerToOptionWrapper(Integer code) {
        // 这里简化处理，实际使用时应该根据具体字段名来调用
        return optionWrapperConverter.fromDatabaseSingleValue("default", code);
    }
    
    /**
     * 为特定字段创建专门的转换方法
     */
    @Named("genderToOptionWrapper")
    protected OptionWrapper genderToOptionWrapper(Integer code) {
        return optionWrapperConverter.fromDatabaseSingleValue("gender", code);
    }
    
    @Named("ageGroupToOptionWrapper")
    protected OptionWrapper ageGroupToOptionWrapper(Integer code) {
        return optionWrapperConverter.fromDatabaseSingleValue("ageGroup", code);
    }
    
    @Named("sourceToOptionWrapper")
    protected OptionWrapper sourceToOptionWrapper(Integer code) {
        return optionWrapperConverter.fromDatabaseSingleValue("source", code);
    }
    
    @Named("homeVisitStatusToOptionWrapper")
    protected OptionWrapper homeVisitStatusToOptionWrapper(Integer code) {
        return optionWrapperConverter.fromDatabaseSingleValue("homeVisitStatus", code);
    }
    
    @Named("outputIntentionToOptionWrapper")
    protected OptionWrapper outputIntentionToOptionWrapper(Integer code) {
        return optionWrapperConverter.fromDatabaseSingleValue("outputIntention", code);
    }
    
    @Named("membershipTypeToOptionWrapper")
    protected OptionWrapper membershipTypeToOptionWrapper(Integer code) {
        return optionWrapperConverter.fromDatabaseSingleValue("membershipType", code);
    }
    
    @Named("purchaseIntentionToOptionWrapper")
    protected OptionWrapper purchaseIntentionToOptionWrapper(Integer code) {
        return optionWrapperConverter.fromDatabaseSingleValue("purchaseIntention", code);
    }
}
