package com.qudian.idle.customer.center.infrastructure.repository.database.po.group;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>文件名称:com.qudian.idle.customer.center.infrastructure.repository.database.po.group.CustomerGroup</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("customer_group")
public class CustomerGroupPO {
    private Long id;
    private String groupName; //客户组名称
    private Long managerId; //客户经理ID
    private String remark; //备注
    private Integer deleteFlag; //是否删除 1已删除 0 未删除
    private LocalDateTime createdTime; //创建时间
    private LocalDateTime updatedTime; //修改时间
}
