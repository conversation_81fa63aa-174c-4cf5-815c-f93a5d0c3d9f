# OptionWrapper 使用说明

## 问题背景

在处理前端传入的选项数据时，经常遇到以下问题：

1. 前端传入格式：`{"gender":[{"code":1, "title":"male"}]}`
2. 后端需要提取其中的 `code` 值存储到数据库
3. 有些字段是单选（如性别），有些是多选（如偏好品牌）
4. 如果直接定义 `List<OptionRecord>` 字段，JSON结构会变成 `{"gender":{"records":[...]}}`，不符合前端期望

## 解决方案

`OptionWrapper` 类通过以下方式解决了上述问题：

### 1. 自动JSON序列化/反序列化

```java
@JsonCreator
public static OptionWrapper fromList(List<OptionRecord> options) {
    return new OptionWrapper(options);
}

@JsonValue
public List<OptionRecord> toList() {
    return this.options;
}
```

这样确保了JSON格式始终是 `[{"code":1, "title":"male"}]`，而不会出现额外的嵌套结构。

### 2. 便捷的数据提取方法

```java
// 获取单个code值（用于单选字段）
public Integer getSingleCode()

// 获取多个code值（用于多选字段）
public List<Integer> getMultipleCodes()

// 获取单个title值
public String getSingleTitle()

// 获取多个title值
public List<String> getMultipleTitles()
```

## 使用示例

### 1. 在VO类中使用

```java
@Data
public class CustomerEditReqVO {
    private OptionWrapper gender; // 性别 - 单选
    private OptionWrapper preferType; // 偏好类型 - 多选
    
    // 便捷方法
    public Integer getGenderCode() {
        return gender != null ? gender.getSingleCode() : null;
    }
    
    public List<Integer> getPreferTypeCodes() {
        return preferType != null ? preferType.getMultipleCodes() : Collections.emptyList();
    }
}
```

### 2. 前端JSON格式

```json
{
    "gender": [{"code": 1, "title": "男"}],
    "preferType": [
        {"code": 1, "title": "包袋"},
        {"code": 2, "title": "珠宝"},
        {"code": 3, "title": "手表"}
    ]
}
```

### 3. 数据库转换

使用 `OptionWrapperConverter` 进行转换：

```java
@Component
public class OptionWrapperConverter {
    
    // OptionWrapper -> 数据库单值字段
    public Integer toDatabaseSingleValue(OptionWrapper optionWrapper) {
        return optionWrapper != null ? optionWrapper.getSingleCode() : null;
    }
    
    // 数据库单值字段 -> OptionWrapper
    public OptionWrapper fromDatabaseSingleValue(String fieldName, Integer code) {
        if (code == null) return new OptionWrapper();
        String title = getTitleByCode(fieldName, code);
        return OptionWrapper.fromSingle(code, title);
    }
}
```

### 4. MapStruct集成

```java
@Mapper(componentModel = "spring")
public abstract class CustomerBaseStruct {
    
    @Autowired
    protected OptionWrapperConverter converter;
    
    @Mapping(target = "gender", source = "gender", qualifiedByName = "optionWrapperToInteger")
    public abstract CustomerBasePO vo2PO(CustomerEditReqVO vo);
    
    @Named("optionWrapperToInteger")
    protected Integer optionWrapperToInteger(OptionWrapper wrapper) {
        return converter.toDatabaseSingleValue(wrapper);
    }
}
```

## 主要优势

1. **JSON格式兼容**：确保前后端JSON格式一致，无额外嵌套
2. **类型安全**：提供强类型的数据提取方法
3. **单多选统一**：同一个类型处理单选和多选场景
4. **空值安全**：所有方法都进行了空值检查
5. **易于扩展**：可以轻松添加新的转换方法

## 迁移指南

### 从 List<OptionRecord> 迁移到 OptionWrapper

1. **替换字段类型**：
   ```java
   // 原来
   private List<OptionRecord> gender;
   
   // 现在
   private OptionWrapper gender;
   ```

2. **更新数据提取逻辑**：
   ```java
   // 原来
   Integer genderCode = gender != null && !gender.isEmpty() ? gender.get(0).getCode() : null;
   
   // 现在
   Integer genderCode = gender != null ? gender.getSingleCode() : null;
   ```

3. **更新MapStruct映射**：
   ```java
   // 添加转换方法
   @Mapping(target = "gender", source = "gender", qualifiedByName = "optionWrapperToInteger")
   ```

## 注意事项

1. **字段命名**：在 `OptionWrapperConverter` 中需要为每个字段配置正确的 title 映射
2. **序列化配置**：确保 Jackson 配置正确，支持 `@JsonCreator` 和 `@JsonValue`
3. **空值处理**：始终检查 OptionWrapper 是否为 null
4. **性能考虑**：对于大量数据的场景，考虑缓存 title 映射关系

## 扩展功能

可以根据需要添加更多功能：

1. **JSON序列化支持**：添加与数据库JSON字段的转换
2. **验证功能**：添加选项值的有效性验证
3. **国际化支持**：根据语言环境返回不同的 title
4. **缓存优化**：缓存常用的选项映射关系
